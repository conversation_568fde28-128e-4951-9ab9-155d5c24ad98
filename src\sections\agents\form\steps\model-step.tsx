import { Stack, Typo<PERSON>, Grid, Card, CardContent, Checkbox, FormControlLabel } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { AgentFormValues } from '../config/agent-form-config';
import ServiceSearchBar from '../components/service-search-bar';
import { useAgentForm } from '../use-agent-form';

// ----------------------------------------------------------------------

interface ModelStepProps {
  // Add any specific props if needed
}

export function ModelStep(_props: ModelStepProps) {
  // Get data and handlers from the main form hook
  const {
    LLM_MODEL: filteredChannels,
    selectedChannels,
    handleChannelToggle,
    channelSearchQuery,
    handleChannelSearchChange,
  } = useAgentForm({ agent: null });

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Choose Channels
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={channelSearchQuery}
          onChange={handleChannelSearchChange}
          placeholder="Search channels..."
        />
        <Grid container spacing={2}>
          {filteredChannels.map((channel) => {
            const isSelected = selectedChannels.includes(channel.value);
            return (
              <Grid item xs={12} sm={12} key={channel.value}>
                <Card
                  variant="outlined"
                  sx={{
                    cursor: 'pointer',
                    backgroundColor: 'divider',
                  }}
                  onClick={() => handleChannelToggle(channel.value)}
                >
                  <CardContent>
                    <FormControlLabel
                      control={<Checkbox checked={isSelected} />}
                      label={
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Iconify icon={channel.icon} />
                          <Typography variant="subtitle2">{channel.label}</Typography>
                        </Stack>
                      }
                      sx={{ width: '100%' }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </Stack>
    </>
  );
}

export default ModelStep;
